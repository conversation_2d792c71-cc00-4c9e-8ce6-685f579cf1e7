"use client";

import React, {
  useRef,
  useState,
  forwardRef,
  useEffect,
  useMemo,
  memo,
  useCallback,
} from "react";
import { AppButton, AppDataTableRealtime, AppSelectFilter } from "@/components";
import { CalendarIcon, FilterIcon } from "@/assets/icons";
import DatePicker from "react-datepicker";
import moment from "moment";
import { useMediaQuery } from "react-responsive";
import { useWindowSize } from "@/hooks";
import rf from "@/services/RequestFactory";
import { TUserTrade } from "@/types/trade";
import { usePairContext } from "@/app/trade/[symbol]/provider";
import { EOrderSideParam, OPTIONS_SIDE } from "../OrderHistory";
import { SelectSideOrder } from "../OrderExchange/components/SelectSideOrder";
import TradeItem from "./components/TradeItem";
import { BROADCAST_EVENTS } from "@/libs/broadcast";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";

const RESOLUTIONS = [
  {
    name: "1 Day",
    value: "1d",
  },
  {
    name: "1 Week",
    value: "1w",
  },
  {
    name: "1 Month",
    value: "1m",
  },
  {
    name: "3 Month",
    value: "3m",
  },
];

export const CustomDateInput = forwardRef<
  HTMLDivElement,
  { value?: any; onClick?: () => void; isInPair: boolean }
>(({ value, onClick, isInPair = false }, ref) => {
  return (
    <div
      onClick={onClick}
      ref={ref}
      className={` text-white-900 border-white-100  flex cursor-pointer items-center gap-1 rounded-[4px] border ${
        isInPair
          ? "bg-white-100  body-sm-medium-12 rounded-[4px] px-1 py-[1px]"
          : "body-md-medium-14 rounded-[6px] p-2"
      }`}
    >
      {value ? value : <span className="text-white-300">Select date</span>}
      <CalendarIcon className={`${isInPair ? "" : "h-[17px] w-[17px]"}`} />
    </div>
  );
});

CustomDateInput.displayName = "CustomDateInput";

export const TableTradeHistory = memo(
  ({ isInPair = false }: { isInPair: boolean }) => {
    const { pairSetting } = usePairContext();
    const [side, setSide] = useState<string>(EOrderSideParam.All);
    const dataTableRef = useRef<HTMLDivElement | null>(null);
    const [resolution, setResolution] = useState<string>("1d");
    const [dateRange, setDateRange] = useState<[Date, Date]>([] as any);
    const [startDate, endDate] = useMemo(() => {
      return dateRange;
    }, [dateRange]);

    const [base, setBase] = useState<string>("");
    const [quote, setQuote] = useState<string>("");

    const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
    const { windowHeight } = useWindowSize();
    const isLayoutAdvanced = useSelector(
      (state: RootState) => state.metadata.isLayoutAdvanced
    );

    // Get pair settings from store
    const { activePairSettings } = useSelector(
      (state: RootState) => state.pairSettings
    );

    // Generate base asset options from pair settings
    const baseAssetOptions = useMemo(() => {
      const baseAssets = new Set<string>();
      activePairSettings.forEach((pair: { baseAsset?: string }) => {
        if (pair.baseAsset) {
          baseAssets.add(pair.baseAsset);
        }
      });

      const sortedBaseAssets = Array.from(baseAssets).sort();
      const options = sortedBaseAssets.map((asset) => ({
        label: asset,
        value: asset,
      }));

      return [{ label: "All", value: "" }, ...options];
    }, [activePairSettings]);

    const getMyTrades = useCallback(
      async (params: any) => {
        try {
          if (!startDate) {
            return {
              cursor: null,
              data: [],
            };
          }

          const tradeParams = {
            ...params,
            symbol: pairSetting?.symbol,
            startDate: startDate.valueOf(),
            endDate: endDate.valueOf(),
            side,
            baseAsset: base || undefined, // Include base filter if selected
          };

          const { docs, cursor } = await rf
            .getRequest("TradeRequest")
            .getUserTrades(tradeParams);
          return {
            cursor,
            data: docs || [],
          };
        } catch (err) {
          console.log(err, "getMyTrades error");
          return { cursor: null, data: [] };
        }
      },
      [startDate, endDate, pairSetting?.symbol, side, base]
    );

    useEffect(() => {
      if (resolution === "3m") {
        setDateRange([
          moment().subtract(3, "months").toDate(),
          moment().toDate(),
        ]);
      }
      if (resolution === "1m") {
        setDateRange([
          moment().subtract(1, "months").toDate(),
          moment().toDate(),
        ]);
      }
      if (resolution === "1w") {
        setDateRange([
          moment().subtract(7, "days").toDate(),
          moment().toDate(),
        ]);
      }
      if (resolution === "1d") {
        setDateRange([
          moment().subtract(1, "days").toDate(),
          moment().toDate(),
        ]);
      }
    }, [resolution]);

    const tableHeight = useMemo(() => {
      if (isInPair) {
        if (isLayoutAdvanced) {
          return 450;
        }
        return windowHeight - 1010;
      }
      if (isMobile) {
        return windowHeight - 50 - 40 - 36;
      }
      return 200;
    }, [windowHeight, isMobile]);

    const handleResetSearch = () => {
      setResolution("1d");
      setSide(EOrderSideParam.All);
    };

    return (
      <div className="w-full">
        {!isInPair ? (
          <>
            <div className="my-4 hidden items-center lg:flex">
              <DatePicker
                selectsRange
                startDate={startDate}
                endDate={endDate}
                onChange={(update: any) => setDateRange(update)}
                placeholderText="YYYY/MM/DD - YYYY/MM/DD"
                dateFormat="YYYY/MM/dd"
                customInput={<CustomDateInput isInPair={isInPair} />}
                className="w-full rounded border p-2"
              />
              <div className="ml-2 flex gap-2">
                <div className="min-w-[120px]">
                  <AppSelectFilter
                    options={baseAssetOptions}
                    value={base}
                    setValue={setBase}
                    title={"Base"}
                  />
                </div>
                <div className="min-w-[120px]">
                  <AppSelectFilter
                    options={OPTIONS_SIDE}
                    value={side}
                    setValue={setSide}
                    title={"Side"}
                  />
                </div>

                <AppButton className="w-[80px]">Search</AppButton>

                <div
                  className="body-sm-medium-12 flex cursor-pointer items-center px-2"
                  onClick={handleResetSearch}
                >
                  Reset
                </div>
              </div>
            </div>
            <div className="flex items-center justify-end px-4 py-2 lg:hidden">
              <div>
                <FilterIcon />
              </div>
            </div>
          </>
        ) : (
          <>
            <div className="hidden py-2 lg:flex">
              <div className="flex">
                <div className="mr-2 flex items-center gap-2">
                  {RESOLUTIONS.map((item) => {
                    return (
                      <div
                        className={`body-sm-medium-12 text-white-900 cursor-pointer border px-1 ${
                          item.value === resolution
                            ? "border-white-100 bg-white-100 rounded-[4px]"
                            : "border-transparent"
                        }`}
                        onClick={() => setResolution(item.value)}
                        key={item.value}
                      >
                        {item.name}
                      </div>
                    );
                  })}
                </div>

                <DatePicker
                  selectsRange
                  startDate={startDate}
                  endDate={endDate}
                  onChange={(update: any) => setDateRange(update)}
                  placeholderText="YYYY/MM/DD - YYYY/MM/DD"
                  dateFormat="YYYY/MM/dd"
                  customInput={<CustomDateInput isInPair={isInPair} />}
                  className="w-full rounded border p-2"
                />

                <div className="body-sm-medium-12 text-white-900 border-white-100 bg-white-100 ml-2 flex cursor-pointer items-center rounded-[4px] border px-1">
                  Search
                </div>

                <div
                  onClick={() => setResolution("1d")}
                  className="body-sm-medium-12 text-white-900 ml-2 flex cursor-pointer items-center px-1"
                >
                  Reset
                </div>
              </div>
            </div>
          </>
        )}

        <AppDataTableRealtime
          minWidth={1108}
          ref={dataTableRef}
          getData={getMyTrades}
          overrideBodyClassName="w-full"
          handleAddNewItem={{
            broadcastName: BROADCAST_EVENTS.USER_TRADE_UPDATE,
            fieldKey: "id",
            formatter: (data: TUserTrade) => {
              return data;
            },
          }}
          renderHeader={() => {
            if (isMobile) {
              return null;
            }
            return (
              <>
                <div className="flex w-full items-center">
                  <div className="body-sm-regular-12 text-white-500 flex w-[14%] min-w-[120px] items-center px-2 py-1.5 ">
                    Order No.
                  </div>
                  <div className="body-sm-regular-12 text-white-500 flex w-[10%] min-w-[100px] items-center px-2 py-1.5 ">
                    Date
                  </div>
                  <div className="body-sm-regular-12 text-white-500 w-[10%] min-w-[100px] px-2 py-1.5">
                    Pair
                  </div>
                  <div className="body-sm-regular-12 text-white-500 w-[10%] min-w-[100px] px-2 py-1.5 text-left">
                    <SelectSideOrder side={side} setSide={setSide} />
                  </div>
                  <div className="body-sm-regular-12 text-white-500 w-[10%] min-w-[100px] px-2 py-1.5 text-left">
                    <div className="flex items-center gap-2">Price</div>
                  </div>
                  <div className="body-sm-regular-12 text-white-500 w-[10%] min-w-[100px] px-2 py-1.5 text-left">
                    Excuted
                  </div>
                  <div className="body-sm-regular-12 text-white-500 w-[9%] min-w-[100px] px-2 py-1.5 text-left">
                    Fee
                  </div>
                  <div className="body-sm-regular-12 text-white-500 w-[9%] min-w-[100px] px-2 py-1.5 text-left">
                    Role
                  </div>
                  <div className="body-sm-regular-12 text-white-500 w-[9%] min-w-[100px] px-2 py-1.5 text-left">
                    Total
                  </div>
                  <div className="body-sm-regular-12 text-white-500 w-[9%] min-w-[120px] px-2 py-1.5 text-left">
                    Total in USDT
                  </div>
                </div>
              </>
            );
          }}
          renderRow={(item: TUserTrade, index: number) => {
            return (
              <TradeItem key={index} trade={item} pairSetting={pairSetting} />
            );
          }}
          height={tableHeight}
          minHeight={300}
        />
      </div>
    );
  }
);

TableTradeHistory.displayName = "TableTradeHistory";
