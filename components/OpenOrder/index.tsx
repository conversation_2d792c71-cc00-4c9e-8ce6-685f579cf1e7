"use client";

import React, {
  memo,
  useMemo,
  useRef,
  useState,
  useEffect,
  useCallback,
} from "react";
import { CheckboxIcon, CheckboxCheckedIcon, FilterIcon } from "@/assets/icons";
import {
  AppButtonSort,
  AppButton,
  AppSelectFilter,
  AppDataTableRealtime,
} from "@/components";
import { useMediaQuery } from "react-responsive";
import { useWindowSize } from "@/hooks";
import { ModalConfirm } from "@/modals/ModalConfirm";
import { SelectSideOrder } from "../OrderExchange/components/SelectSideOrder";
import { OpenOrderItems } from "./components/OpenOrderItems";
import rf from "@/services/RequestFactory";
import { EOrderStatus, TOpenOrder } from "@/types/order";
import { errorMsg, successMsg } from "@/libs/toast";
import { EOrderSideParam, OPTIONS_SIDE } from "../OrderHistory";
import { ModalFilterOpenOrder } from "../../modals/ModalFilterOpenOrder";
import { BROADCAST_EVENTS } from "@/libs/broadcast";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";

const OPTIONS_FILTER = [
  {
    label: "All",
    value: "",
  },
  {
    label: "Limit Order",
    value: "LIMIT",
  },
  {
    label: "Stop Limit",
    value: "STOP_LIMIT",
  },
];

export const TableOpenOrder = memo(
  ({ isInPair = false }: { isInPair?: boolean }) => {
    const [sortBy, setSortBy] = useState<string>("");
    const [side, setSide] = useState<EOrderSideParam>(EOrderSideParam.All);
    const [sortType, setSortType] = useState<string>("");
    const [isHideOtherPair, setIsHideOtherPair] = useState<boolean>(false);
    const [isShowModalConfirmCancelOrder, setIsShowModalConfirmCancelOrder] =
      useState<boolean>(false);
    const [isShowModalFilter, setIsShowModalFilter] = useState<boolean>(false);

    const [typeOrder, setTypeOrder] = useState<string>("");
    const [pairSelected, setPairSelected] = useState<string>("");

    const dataTableRef = useRef<HTMLDivElement | null>(null);
    const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
    const { windowHeight } = useWindowSize();
    const [mounted, setMounted] = useState(false);
    const isLayoutAdvanced = useSelector(
      (state: RootState) => state.metadata.isLayoutAdvanced
    );

    // Get pair settings from store
    const { activePairSettings } = useSelector(
      (state: RootState) => state.pairSettings
    );

    useEffect(() => {
      setMounted(true);
    }, []);

    const pairsOptions = useMemo(() => {
      const symbols: { label: string; value: string }[] =
        activePairSettings?.map((item: { symbol: string }) => ({
          label: item.symbol,
          value: item.symbol,
        }));

      return [{ label: "All", value: "" }, ...symbols];
    }, [activePairSettings]);

    const getData = useCallback(
      async (params: any) => {
        const filterParams = {
          ...params,
          side: side || EOrderSideParam.All,
        };

        if (sortBy) {
          filterParams.sort_by = sortBy;
        }

        if (sortType) {
          filterParams.order = sortType;
        }

        if (pairSelected) {
          filterParams.symbol = pairSelected;
        }

        try {
          const { docs, cursor } = await rf
            .getRequest("OrderRequest")
            .getOpenOrders({
              ...filterParams,
              type: "Limit",
            });

          return {
            cursor,
            data: docs || [],
          };
        } catch (err) {
          console.log(err, "getData error");
          return { data: [], cursor: null };
        }
      },
      [side, sortBy, sortType, pairSelected]
    );

    const tableHeight = useMemo(() => {
      if (isInPair) {
        if (isLayoutAdvanced) {
          return 450;
        }
        return windowHeight - 1010;
      }
      if (isMobile) {
        return windowHeight - 50 - 40 - 36;
      }
      return 200;
    }, [windowHeight, isMobile, isInPair]);

    const handleShowCancelAllModal = useCallback(() => {
      setIsShowModalConfirmCancelOrder(true);
    }, []);

    const handleCancelAllOrder = useCallback(async () => {
      try {
        await rf.getRequest("OrderRequest").cancelAllOpenOrders();

        successMsg("Cancel order successfully");
        (dataTableRef.current as any)?.removeAll();
        setIsShowModalConfirmCancelOrder(false);
      } catch (err: any) {
        errorMsg(`Cancel all orders failed with error: ${err?.message}`);
        console.log(err, "cancel all order error");
      }
    }, []);

    const onConfirmCancelOrder = useCallback(async (orderId: string) => {
      try {
        await rf.getRequest("OrderRequest").cancelOpenOrder(orderId);

        successMsg("Cancel order successfully");
      } catch (err: any) {
        errorMsg(`Cancel order failed with error: ${err?.message}`);
        console.log(err, "cancel order error");
      }
    }, []);

    const handleFormatNewItem = useCallback((data: TOpenOrder) => {
      if (!data || !data.status || !data.order_id) {
        return null;
      }

      // Handle rejected orders
      if ([EOrderStatus.REJECTED].includes(data.status)) {
        return null;
      }

      // Handle canceled or filled orders
      if ([EOrderStatus.CANCELLED, EOrderStatus.FILLED].includes(data.status)) {
        try {
          if (dataTableRef.current) {
            (dataTableRef.current as any).removeItem("order_id", data.order_id);
          }
        } catch (error) {
          console.error("Error removing item:", error);
        }
        return null;
      }

      return data;
    }, []);

    const handleAddNewItem = useMemo(
      () => ({
        broadcastName: BROADCAST_EVENTS.ORDER_UPDATED,
        fieldKey: "order_id",
        formatter: handleFormatNewItem,
      }),
      [handleFormatNewItem]
    );

    const renderHeader = useCallback(() => {
      if (isMobile) {
        return null;
      }
      return (
        <div className="flex w-full items-center">
          <div className="body-sm-regular-12 text-white-500 flex w-[14%] min-w-[100px] items-center px-2 py-1.5 ">
            Date
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[14%] min-w-[100px] px-2 py-1.5 text-left">
            Pair
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[12%] min-w-[100px] px-2 py-1.5 text-left">
            <SelectSideOrder
              side={side}
              setSide={(value: string) => setSide(value as EOrderSideParam)}
            />
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[12%] min-w-[100px] px-2 py-1.5 text-left">
            <div className="flex items-center gap-2">
              Price
              <AppButtonSort
                value="Price"
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[12%] min-w-[100px] px-2 py-1.5 text-left">
            Amount
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[12%] min-w-[100px] px-2 py-1.5 text-left">
            Filled
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[12%] min-w-[100px] px-2 py-1.5 text-left">
            Total
          </div>
          <div className="body-sm-regular-12 text-brand-500 w-[12%] min-w-[100px] px-2 py-1.5">
            <div className="flex justify-center">
              <div
                className="body-sm-medium-12 text-brand-500 flex cursor-pointer items-center gap-1"
                onClick={handleShowCancelAllModal}
              >
                Cancel All
              </div>
            </div>
          </div>
        </div>
      );
    }, [
      isMobile,
      side,
      sortBy,
      sortType,
      setSortBy,
      setSortType,
      handleShowCancelAllModal,
    ]);

    const renderRow = useCallback(
      (item: TOpenOrder, index: number) => {
        return (
          <OpenOrderItems
            key={index}
            isInPair={isInPair}
            order={item}
            onConfirmCancelOrder={onConfirmCancelOrder}
          />
        );
      },
      [isInPair, onConfirmCancelOrder]
    );

    if (!mounted) {
      return <></>;
    }

    return (
      <div>
        {isInPair ? (
          <div className="flex items-center justify-between px-4 py-2 lg:hidden">
            <div
              className="body-md-regular-14 flex items-center gap-2"
              onClick={() => setIsHideOtherPair(!isHideOtherPair)}
            >
              {isHideOtherPair ? <CheckboxCheckedIcon /> : <CheckboxIcon />}
              Hide Other Pairs
            </div>
            <div>
              <AppButton
                variant="secondary"
                size="small"
                className="px-2 !text-[10px]"
                onClick={handleShowCancelAllModal}
              >
                Cancel All
              </AppButton>
            </div>
          </div>
        ) : (
          <>
            <div className="my-4 hidden gap-2 lg:flex">
              <div className="min-w-[120px]">
                <AppSelectFilter
                  options={OPTIONS_FILTER}
                  value={typeOrder}
                  setValue={setTypeOrder}
                  title={"Filter"}
                />
              </div>
              <div className="min-w-[120px]">
                <AppSelectFilter
                  options={pairsOptions}
                  value={pairSelected}
                  setValue={setPairSelected}
                  title={"Pair"}
                />
              </div>
              <div className="min-w-[120px]">
                <AppSelectFilter
                  options={OPTIONS_SIDE}
                  value={side}
                  setValue={(value: string) =>
                    setSide(value as EOrderSideParam)
                  }
                  title={"Side"}
                />
              </div>
            </div>
            <div className="flex items-center justify-between px-4 py-2 lg:hidden ">
              <div>
                <div
                  className="body-sm-medium-12 flex cursor-pointer items-center gap-1 text-green-500"
                  onClick={handleShowCancelAllModal}
                >
                  Cancel All
                </div>
              </div>
              <div onClick={() => setIsShowModalFilter(true)}>
                <FilterIcon />
              </div>
            </div>
          </>
        )}

        <div className="w-full">
          <AppDataTableRealtime
            minWidth={1108}
            ref={dataTableRef}
            getData={getData}
            overrideBodyClassName="w-full"
            handleAddNewItem={handleAddNewItem}
            renderHeader={renderHeader}
            renderRow={renderRow}
            height={tableHeight}
            minHeight={300}
          />
        </div>

        {isShowModalConfirmCancelOrder && (
          <ModalConfirm
            isOpen={isShowModalConfirmCancelOrder}
            onClose={() => setIsShowModalConfirmCancelOrder(false)}
            onConfirm={handleCancelAllOrder}
            titleAction="Confirm"
            description={"Are you sure you want to cancel all open orders?"}
          />
        )}

        {isShowModalFilter && (
          <ModalFilterOpenOrder
            isOpen={isShowModalFilter}
            onClose={() => setIsShowModalFilter(false)}
            side={side}
            setSide={setSide}
            typeOrder={typeOrder}
            setTypeOrder={setTypeOrder}
            pair={pairSelected}
            setPair={setPairSelected}
            optionsType={OPTIONS_FILTER}
            optionsPair={pairsOptions}
          />
        )}
      </div>
    );
  }
);

TableOpenOrder.displayName = "TableOpenOrder";
