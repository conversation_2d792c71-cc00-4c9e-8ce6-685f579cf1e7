"use client";

import { SellOrderForm } from "./SellOrderForm";
import { BuyOrderForm } from "./BuyOrderForm";
import { EOrderType } from "..";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import { useEffect, useState } from "react";
import { EOrderbook } from "@/types/OrderBook";

type TSimpleOrder = {
  orderType: EOrderType;
};
export const SimpleOrder = ({ orderType }: TSimpleOrder) => {
  const [selectedOrderbook, setSelectedOrderbook] = useState<{
    price: string;
    sellAmount: string;
    buyAmount: string;
  }>({
    price: "0",
    sellAmount: "0",
    buyAmount: "0",
  });

  const handleOrderbookSelected = (data: TBroadcastEvent) => {
    const { price, amount, orderbookType } = data?.detail;

    if (!price) {
      return;
    }

    setSelectedOrderbook({
      price,
      sellAmount: orderbookType == EOrderbook.BID ? amount : "",
      buyAmount: orderbookType == EOrderbook.ASK ? amount : "",
    });
  };

  useEffect(() => {
    AppBroadcast.on(
      BROADCAST_EVENTS.ORDERBOOK_SELECTED,
      handleOrderbookSelected
    );
    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.ORDERBOOK_SELECTED,
        handleOrderbookSelected
      );
    };
  }, []);

  return (
    <div className="mt-2 grid grid-cols-2 gap-4">
      <div>
        <BuyOrderForm
          orderType={orderType}
          defaultPrice={selectedOrderbook?.price}
          defaultAmount={selectedOrderbook?.buyAmount}
        />
      </div>
      <div>
        <SellOrderForm
          orderType={orderType}
          defaultPrice={selectedOrderbook?.price}
          defaultAmount={selectedOrderbook?.sellAmount}
        />
      </div>
    </div>
  );
};
