"use client";

import React, { useState } from "react";
import { ModalOrderForm } from "@/modals";
import { AppButton } from "@/components";

export enum EOrderSide {
  BUY = "BUY",
  SELL = "SELL",
}

const OrderFormMobile = () => {
  const [isShowModalOrderForm, setIsShowModalOrderForm] =
    useState<boolean>(false);
  const [sideOrder, setSideOrder] = useState<EOrderSide>(EOrderSide.BUY);

  return (
    <>
      <div
        className="bg-white-50 fixed bottom-0 left-0 right-0 grid grid-cols-2 gap-3 px-2 py-4"
        style={{
          backdropFilter: "blur(calc(var(--24, 24px) / 2))",
        }}
      >
        <AppButton
          variant="buy"
          size="large"
          onClick={() => {
            setIsShowModalOrderForm(true);
            setSideOrder(EOrderSide.BUY);
          }}
        >
          Buy
        </AppButton>
        <AppButton
          variant="sell"
          size="large"
          onClick={() => {
            setIsShowModalOrderForm(true);
            setSideOrder(EOrderSide.SELL);
          }}
        >
          Sell
        </AppButton>
      </div>

      {isShowModalOrderForm && (
        <ModalOrderForm
          side={sideOrder}
          isOpen={isShowModalOrderForm}
          onClose={() => setIsShowModalOrderForm(false)}
        />
      )}
    </>
  );
};
export default OrderFormMobile;
