"use client";

import { EOrderType } from "..";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import React, { useEffect, useState } from "react";
import { EOrderSide } from "../OrderFormMobile";
import { BuyOrderForm } from "../simple-order/BuyOrderForm";
import { SellOrderForm } from "../simple-order/SellOrderForm";
import { EOrderbook } from "@/types/OrderBook";

type TSimpleOrder = {
  orderType: EOrderType;
};

export const AdvancedOrder = ({ orderType }: TSimpleOrder) => {
  const [selectedOrderbook, setSelectedOrderbook] = useState<{
    price: string;
    sellAmount: string;
    buyAmount: string;
  }>({
    price: "0",
    sellAmount: "0",
    buyAmount: "0",
  });
  const [orderSide, setOrderSide] = useState<EOrderSide>(EOrderSide.BUY);

  const handleOrderbookSelected = (data: TBroadcastEvent) => {
    const { price, amount, orderbookType } = data?.detail;

    if (!price) {
      return;
    }

    setSelectedOrderbook({
      price,
      sellAmount: orderbookType == EOrderbook.BID ? amount : "",
      buyAmount: orderbookType == EOrderbook.ASK ? amount : "",
    });
  };

  useEffect(() => {
    AppBroadcast.on(
      BROADCAST_EVENTS.ORDERBOOK_SELECTED,
      handleOrderbookSelected
    );
    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.ORDERBOOK_SELECTED,
        handleOrderbookSelected
      );
    };
  }, []);

  return (
    <div className="p-4">
      <div className="bg-white-50 mb-2.5 grid grid-cols-2 rounded-[4px] p-[2px]">
        <div
          className="bg-white-100 flex w-full cursor-pointer rounded-l-[4px]"
          onClick={() => setOrderSide(EOrderSide.BUY)}
        >
          <div
            className={`body-sm-semibold-12 flex flex-1 items-center justify-center rounded-l-[4px] text-center ${
              orderSide === EOrderSide.BUY ? "bg-green-500" : ""
            } `}
          >
            Buy
          </div>
          {orderSide === EOrderSide.BUY && (
            <div className="border-y-12 h-0 w-0 border-l-[12px] border-y-transparent border-l-green-500" />
          )}
        </div>
        <div
          className="bg-white-100 flex w-full cursor-pointer rounded-r-[4px]"
          onClick={() => setOrderSide(EOrderSide.SELL)}
        >
          {orderSide === EOrderSide.SELL && (
            <div className="border-y-12 h-0 w-0 border-r-[12px] border-y-transparent border-r-red-500" />
          )}

          <div
            className={`body-sm-semibold-12 flex flex-1 items-center justify-center rounded-r-[4px] text-center ${
              orderSide === EOrderSide.SELL ? "bg-red-500" : ""
            } `}
          >
            Sell
          </div>
        </div>
      </div>

      {orderSide === EOrderSide.BUY ? (
        <BuyOrderForm
          orderType={orderType}
          defaultPrice={selectedOrderbook?.price}
          defaultAmount={selectedOrderbook?.buyAmount}
        />
      ) : (
        <SellOrderForm
          orderType={orderType}
          defaultPrice={selectedOrderbook?.price}
          defaultAmount={selectedOrderbook?.sellAmount}
        />
      )}
    </div>
  );
};
