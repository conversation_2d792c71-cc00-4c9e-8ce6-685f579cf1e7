"use client";

import { useState } from "react";
import { SimpleOrder } from "./simple-order";
import { AdvancedOrder } from "./advanced-order";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";

export enum EOrderType {
  LIMIT = "LIMIT",
  MARKET = "MARKET",
}

export const OrderForm = () => {
  const [orderType, setOrderType] = useState<EOrderType>(EOrderType.LIMIT);
  const isLayoutAdvanced = useSelector(
    (state: RootState) => state.metadata.isLayoutAdvanced
  );

  return (
    <div className="min-h-[320px] w-full">
      <div
        className={`body-md-medium-14 flex ${
          isLayoutAdvanced ? "border-white-100 border-b" : ""
        }`}
      >
        <div
          onClick={() => setOrderType(EOrderType.LIMIT)}
          className={`cursor-pointer px-3 py-2 ${
            orderType === EOrderType.LIMIT
              ? "border-white-1000 border-b"
              : "text-white-500"
          }`}
        >
          Limit
        </div>
        <div
          onClick={() => setOrderType(EOrderType.MARKET)}
          className={`cursor-pointer px-3 py-2 ${
            orderType === EOrderType.MARKET
              ? "border-white-1000 border-b"
              : "text-white-500"
          }`}
        >
          Market
        </div>
      </div>

      {isLayoutAdvanced ? (
        <AdvancedOrder orderType={orderType} />
      ) : (
        <SimpleOrder orderType={orderType} />
      )}
    </div>
  );
};
