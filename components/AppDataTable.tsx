"use client";

import { get } from "lodash";
import React, {
  CSSProperties,
  forwardRef,
  ReactNode,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";
import { Virtuoso } from "react-virtuoso";
import clsx from "clsx";

interface AppDataTableProps<T> {
  ref?: React.RefObject<HTMLDivElement>;
  getData: (params: {
    cursor: string | null;
    limit: number;
  }) => Promise<{ data: T[]; cursor: string | null }>;
  renderHeader: () => ReactNode;
  overrideHeaderClassName?: string;
  overrideBodyClassName?: string;
  renderRow: (item: T, index: number) => ReactNode;
  height?: number | string;
  limit?: number;
  containerStyle?: CSSProperties;
  headerStyle?: CSSProperties;
  bodyStyle?: CSSProperties;
  rowStyle?: CSSProperties;
  noDataMessage?: string;
  minWidth?: number;
  minHeight?: number;
  shouldAutoFetchOnInit?: boolean;
  onInitializationDone?: () => void;
}

export const AppDataTable = forwardRef<HTMLDivElement, AppDataTableProps<any>>(
  (
    {
      getData,
      renderHeader,
      renderRow,
      onInitializationDone,
      height = 400,
      limit = 10,
      containerStyle,
      headerStyle,
      bodyStyle,
      rowStyle,
      noDataMessage = "No data",
      overrideHeaderClassName,
      overrideBodyClassName,
      minWidth = 900,
      minHeight,
      shouldAutoFetchOnInit = true,
    },
    ref
  ) => {
    const [items, setItems] = useState<any[]>([]);
    const cursorRef = useRef<string | null>(null);
    const [isEmptyData, setIsEmptyData] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const filterParamsRef = useRef<any>(null);

    useImperativeHandle(
      ref,
      () => {
        return {
          isLoading() {
            return isLoading;
          },

          async refresh() {
            cursorRef.current = null;
            await fetchNewData();
          },

          async appendNewData(newData: any) {
            setItems((prevItems: any[]) => [newData, ...prevItems]);
          },

          async filter(filterParams: any) {
            filterParamsRef.current = filterParams;
            cursorRef.current = null;
            await fetchNewData();
          },

          updateOne(updatedData: any, fieldKey: string, update: any) {
            if (!fieldKey) {
              console.warn(
                "Warning: fieldKey is required for updating an item"
              );
              return;
            }
            setItems((prevItems) => {
              const updatedItems = prevItems.map((item) => {
                if (get(item, fieldKey) === get(updatedData, fieldKey)) {
                  const updatedItem = update(item, updatedData);
                  return updatedItem || item;
                }
                return item;
              });
              return updatedItems;
            });
          },

          removeItem(key: string, value: any) {
            setItems((prev) => {
              return prev.filter((item) => item[key] != value);
            });
          },

          removeAll() {
            setItems([]);
          },

          getItems() {
            return items;
          },
        } as any;
      },
      // eslint-disable-next-line
      [getData]
    );

    const fetchNewData = async () => {
      setIsLoading(true);
      const { data, cursor: newCursor } = await getData({
        cursor: cursorRef?.current || null,
        limit,
        ...filterParamsRef.current,
      });

      setItems(data);
      setIsEmptyData(data?.length < limit);
      cursorRef.current = newCursor;
      setIsLoading(false);
      if (onInitializationDone) {
        onInitializationDone();
      }
    };

    useEffect(() => {
      if (!shouldAutoFetchOnInit) return;
      cursorRef.current = null;
      fetchNewData().then();
      // eslint-disable-next-line
    }, [shouldAutoFetchOnInit, getData]);

    const loadMore = async () => {
      if (isEmptyData || isLoading) return;

      try {
        const { data, cursor: newCursor } = await getData({
          cursor: cursorRef?.current || null,
          limit,
          ...filterParamsRef.current,
        });
        setItems((prevItems) => [...prevItems, ...data]);
        setIsEmptyData(data?.length < limit);
        cursorRef.current = newCursor;
      } catch (error) {
        console.error("Error loading data:", error);
      }
    };

    const renderedHeader = useMemo(() => {
      return renderHeader();
    }, [renderHeader]);

    const renderContentTable = useCallback(() => {
      if (isLoading) {
        return (
          <>
            <div
              style={{
                height,
                ...bodyStyle,
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              Loading...
            </div>
          </>
        );
      }

      if (items?.length === 0) {
        return (
          <div
            style={{
              height,
              ...bodyStyle,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            {noDataMessage}
          </div>
        );
      }

      return (
        <Virtuoso
          className="customer-scroll"
          style={{
            height,
            minHeight,
            ...bodyStyle,
          }}
          data={items}
          endReached={loadMore}
          itemContent={(index, item) => (
            <div
              className={
                overrideBodyClassName
                  ? overrideBodyClassName
                  : `hover:bg-neutral-alpha-50 border-white-100 flex border-b text-[12px] font-normal leading-[1.5]`
              }
              style={{ ...rowStyle }}
            >
              {renderRow(item, index)}
            </div>
          )}
        />
      );
    }, [
      isLoading,
      items,
      renderRow,
      overrideBodyClassName,
      rowStyle,
      bodyStyle,
      loadMore,
    ]);

    return (
      <div className={"overflow-x-auto"}>
        <div
          ref={ref}
          className={clsx(`min-w-[${minWidth}px]`)}
          style={containerStyle}
        >
          {renderedHeader !== null && (
            <div
              className={
                overrideHeaderClassName
                  ? overrideHeaderClassName
                  : "text-neutral-alpha-500 border-white-100 flex min-h-[40px] border-b text-[12px] font-normal leading-[1.5]"
              }
              style={{ ...headerStyle }}
            >
              {renderedHeader}
            </div>
          )}

          {renderContentTable()}
        </div>
      </div>
    );
  }
);

AppDataTable.displayName = "AppDataTable";
