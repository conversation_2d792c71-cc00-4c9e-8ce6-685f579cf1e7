import { memo } from "react";
import { StarIcon, StarActiveIcon } from "@/assets/icons";
import { TPairSetting, Ticker } from "@/types/pair";
import AppNumber from "@/components/AppNumber";
import { getPriceStyle } from "@/utils/helper";

interface MarketPairRowProps {
  item: TPairSetting;
  ticker?: Ticker;
  isShow24hChange: boolean;
  isFavorite: boolean;
  onToggleFavorite: (symbol: string) => void;
  onPairClick: (symbol: string) => void;
}

export const MarketPairRow = memo(
  ({
    item,
    ticker,
    isShow24hChange,
    isFavorite,
    onToggleFavorite,
    onPairClick,
  }: MarketPairRowProps) => {
    return (
      <div
        className="hover:bg-white-50 grid cursor-pointer grid-cols-2"
        onClick={() => onPairClick(item.symbol)}
      >
        <div className="body-sm-regular-12 flex items-center gap-2 p-2 pl-4">
          <div
            onClick={(e) => {
              e.stopPropagation();
              onToggleFavorite(item.symbol);
            }}
            className="cursor-pointer"
          >
            {isFavorite ? <StarActiveIcon /> : <StarIcon />}
          </div>
          <div>{`${item.baseAsset?.toUpperCase()}/${item.quoteAsset?.toUpperCase()}`}</div>
        </div>
        <div className="body-sm-regular-12 grid cursor-pointer grid-cols-2 py-2 pr-2">
          <div className="text-right">
            <AppNumber
              value={ticker?.lastPrice || "0"}
              decimals={item.pricePrecision}
              isFormatLargeNumber={false}
            />
          </div>
          {isShow24hChange ? (
            <div
              className="flex items-center justify-end gap-1"
              style={{ color: getPriceStyle(ticker?.priceChange || "0") }}
            >
              <AppNumber
                value={ticker?.priceChangePercent || "0"}
                decimals={2}
                isFormatLargeNumber={false}
              />
              {"%"}
            </div>
          ) : (
            <div className="text-right">
              <AppNumber
                value={ticker?.quoteVolume || "0"}
                decimals={2}
                isFormatLargeNumber={false}
              />
            </div>
          )}
        </div>
      </div>
    );
  }
);

MarketPairRow.displayName = "MarketPairRow";
