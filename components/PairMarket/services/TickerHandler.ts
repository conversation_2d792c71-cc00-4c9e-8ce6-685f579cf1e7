import { Ticker } from "@/types/pair";
import BigNumber from "bignumber.js";

export type TTickerUpdate = {
  e: "24hrTicker"; // Event type
  E: number; // Event time
  s: string; // Symbol
  p: string; // Price change
  P: string; // Price change percent
  c: string; // Last price
  o: string; // Open price
  h: string; // High price
  l: string; // Low price
  v: string; // Total traded base asset volume
  q: string; // Total traded quote asset volume
};

export default class TickerHandler {
  private marketData: Ticker;
  private symbol: string;
  private previousBaseVolume: string;
  private onMarketDataUpdate: (marketData: Ticker) => void;

  constructor(
    symbol: string,
    onMarketDataUpdate: (marketUpdate: Ticker) => void
  ) {
    this.symbol = symbol;
    this.onMarketDataUpdate = onMarketDataUpdate;
    this.marketData = {
      symbol: this.symbol,
      lastPrice: null,
      highPrice: null,
      lowPrice: null,
      priceChangePercent: null,
      priceChange: null,
      baseVolume: null,
      quoteVolume: null,
      timestamp: null,
      isUp: false,
      isUp24h: false,
      latestChange: null,
    };
    this.previousBaseVolume = "";
  }

  processTickerUpdate(data: TTickerUpdate): void {
    if (data.s?.toLowerCase() !== this.symbol?.toLowerCase()) {
      return;
    }
    const prevPrice = this.marketData.lastPrice;
    const lastPrice = data.c;

    // Update market data
    this.marketData = {
      symbol: this.symbol,
      lastPrice,
      highPrice: data.h,
      lowPrice: data.l,
      priceChangePercent: data.P,
      priceChange: data.p,
      baseVolume: data.v,
      quoteVolume: data.q,
      timestamp: data.E,
      isUp: BigNumber(lastPrice).isGreaterThan(this.marketData.lastPrice || 0),
      latestChange: BigNumber(lastPrice)
        .minus(this.marketData.lastPrice || 0)
        .toFixed(),
      isUp24h: BigNumber(data.p).isGreaterThanOrEqualTo(0),
    };

    if (prevPrice !== lastPrice || this.previousBaseVolume != data.v) {
      this.previousBaseVolume = data.v;
      this.onMarketDataUpdate(this.marketData);
    }
  }
}
