import React, { useCallback, useState } from "react";
import { EOrderbook, OrderBookLevel } from "@/types/OrderBook";
import BigNumber from "bignumber.js";
import { getDecimalPlaces } from "@/utils/helper";
import { useProcessedOrderbook } from "../hooks/useProcessedOrderbook";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";
import AppNumber from "@/components/AppNumber";
import { usePairContext } from "@/app/trade/[symbol]/provider";
import { formatNumber, formatPrice } from "@/utils/format";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";
import Tippy from "@tippyjs/react";

const OrderRow = React.memo(
  ({
    item,
    percentWidth,
    decimal,
    orderbookType,
    quantityDecimal,
    isHideTotal = false,
    index,
    bidAskData,
  }: {
    item: {
      price: string;
      amount: string;
      total: string;
      cumulative: string;
    };
    quantityDecimal: number;
    percentWidth: string;
    decimal: number;
    orderbookType: EOrderbook;
    isHideTotal?: boolean;
    index: number;
    bidAskData: {
      price: string;
      amount: string;
      total: string;
      cumulative: string;
    }[];
  }) => {
    let textColor = "text-red-400";
    let bgColor = "red";

    if (orderbookType === EOrderbook.BID) {
      textColor = "text-green-500";
      bgColor = "green";
    }

    const handleClickItem = () => {
      if (!item.price) return;

      // Calculate cumulative amount from index 0 to selected index
      let cumulativeAmount = new BigNumber(0);
      for (let i = 0; i <= index; i++) {
        const orderItem = bidAskData[i];
        console.log(orderItem, "orderItem");
        if (orderItem) {
          cumulativeAmount = cumulativeAmount.plus(orderItem.amount);
        }
      }

      AppBroadcast.dispatch(BROADCAST_EVENTS.ORDERBOOK_SELECTED, {
        price: item.price,
        amount: cumulativeAmount.toFixed(),
        orderbookType,
      });
    };

    return (
      <div
        className={`relative cursor-pointer ${
          orderbookType === EOrderbook.BID
            ? "hover:lg:border-b"
            : "hover:lg:border-t"
        } border-white-100 border-dashed`}
        onClick={handleClickItem}
      >
        <div className={`grid ${isHideTotal ? "grid-cols-2" : "grid-cols-3"}`}>
          <div
            className={`body-sm-regular-12 px-2 py-1 ${textColor} ${
              orderbookType == EOrderbook.BID && isHideTotal ? "order-2" : ""
            }`}
          >
            <AppNumber
              value={item.price || 0}
              decimals={getDecimalPlaces(decimal)}
              isFormatLargeNumber={false}
            />
          </div>
          <div
            className={`${
              orderbookType == EOrderbook.BID && isHideTotal
                ? "order-1"
                : "text-right"
            } body-sm-regular-12 px-2 py-1 `}
          >
            <AppNumber
              value={item.amount || 0}
              decimals={quantityDecimal}
              isFormatLargeNumber={false}
            />
          </div>

          {!isHideTotal && (
            <div className="body-sm-regular-12 px-2 py-1 text-right">
              <AppNumber value={item?.total || 0} decimals={quantityDecimal} />
            </div>
          )}
        </div>
        <div
          className={`absolute bottom-0 right-0 top-0 h-full bg-${bgColor}-900`}
          style={{ width: `${percentWidth}%` }}
        />
      </div>
    );
  }
);
OrderRow.displayName = "OrderRow";

export const ListOrder = React.memo(
  ({
    orderbook,
    decimal,
    orderbookType,
    displayItemNumber,
    isHideTotal = false,
  }: {
    orderbook: OrderBookLevel[];
    decimal: number;
    orderbookType: EOrderbook;
    displayItemNumber: number;
    isHideTotal?: boolean;
  }) => {
    const [hoveredIndex, setHoveredIndex] = useState<null | number>(null);
    const { bidAskData, largestAmount } = useProcessedOrderbook(
      orderbook,
      decimal,
      displayItemNumber,
      orderbookType === EOrderbook.ASK
    );
    const { pairSetting } = usePairContext();
    const isLayoutAdvanced = useSelector(
      (state: RootState) => state.metadata.isLayoutAdvanced
    );

    const [tooltipData, setTooltipData] = useState<{
      totalQuantity: string;
      totalAmount: string;
      avgPrice: string;
    }>({
      totalQuantity: "0",
      totalAmount: "0",
      avgPrice: "0",
    });

    const calculatePercentWidth = useCallback(
      (total: string) => {
        return BigNumber(total)
          .div(largestAmount || 1)
          .multipliedBy(100)
          .toFixed(0);
      },
      [largestAmount]
    );

    const calculateTooltipData = useCallback(
      (hoveredIndex: number) => {
        if (!bidAskData)
          return { totalQuantity: "0", totalAmount: "0", avgPrice: "0" };

        let totalQuantity = new BigNumber(0);
        let totalAmount = new BigNumber(0);

        for (let i = 0; i <= hoveredIndex; i++) {
          const item = bidAskData[i];
          if (item) {
            totalQuantity = totalQuantity.plus(item.amount);
            totalAmount = totalAmount.plus(item.total);
          }
        }

        const avgPrice = totalQuantity.isZero()
          ? new BigNumber(0)
          : totalAmount.div(totalQuantity);

        return {
          totalQuantity: totalQuantity.toFixed(),
          totalAmount: totalAmount.toFixed(),
          avgPrice: avgPrice.toFixed(),
        };
      },
      [bidAskData]
    );

    const handleRowHover = useCallback(
      (index: number) => {
        setHoveredIndex(index);
        const tooltipData = calculateTooltipData(index);

        setTooltipData(tooltipData);
      },
      [calculateTooltipData]
    );

    const getBg = (index: number) => {
      if (orderbookType === EOrderbook.BID) {
        if (hoveredIndex !== null && index <= hoveredIndex) {
          return "lg:bg-white-50";
        }
        return "";
      }
      if (orderbookType === EOrderbook.ASK) {
        if (hoveredIndex !== null && index >= hoveredIndex) {
          return "lg:bg-white-50";
        }
        return "";
      }
    };

    return (
      <div>
        {bidAskData?.map((item, index) => {
          const percentWidth = calculatePercentWidth(item.total);
          return (
            <Tippy
              key={index}
              content={
                <div className="z-[999] flex hidden min-w-[140px] flex-col gap-1 lg:block ">
                  <div className="flex justify-between">
                    <span className="body-sm-regular-12 text-white-500">
                      Avg Price:
                    </span>
                    <span className="body-sm-medium-12 text-white">
                      {formatPrice(tooltipData.avgPrice, decimal)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="body-sm-regular-12 text-white-500">
                      Sum Quantity:
                    </span>
                    <span className="body-sm-medium-12 text-white">
                      {formatNumber(tooltipData.totalQuantity, 4)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="body-sm-regular-12 text-white-500">
                      Sum Total:
                    </span>
                    <span className="body-sm-medium-12 text-white">
                      {formatNumber(tooltipData.totalAmount, 4)}
                    </span>
                  </div>
                </div>
              }
              placement={isLayoutAdvanced ? "left" : "right"}
              arrow={true}
              delay={[0, 0]}
              animation={false}
            >
              <div
                onMouseEnter={() => handleRowHover(index)}
                onMouseLeave={() => setHoveredIndex(null)}
                className={`${getBg(index)}`}
              >
                <OrderRow
                  key={`${item.price}-${index}`}
                  item={item}
                  isHideTotal={isHideTotal}
                  percentWidth={percentWidth}
                  orderbookType={orderbookType}
                  quantityDecimal={pairSetting?.quantityPrecision || 1}
                  decimal={decimal}
                  index={index}
                  bidAskData={bidAskData || []}
                />
              </div>
            </Tippy>
          );
        })}
      </div>
    );
  }
);
ListOrder.displayName = "ListOrder";
