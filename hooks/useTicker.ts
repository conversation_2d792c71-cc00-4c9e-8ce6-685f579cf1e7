import { useEffect, useRef, useCallback, useState, useMemo } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/store";
import { Ticker } from "@/types/pair";
import { TTickerUpdate } from "@/components/PairMarket/services/TickerHandler";
import {
  getTickerRoom,
  getArrTickerRoom,
  subscribeSocketChannel,
  unsubscribeSocketChannel,
} from "@/libs/socket";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import { setTicker, setTickers } from "@/store/ticker.store";
import { formatTickersUpdate, formatApiTickerToTicker } from "@/utils/format";
import TickerHandler from "@/components/PairMarket/services/TickerHandler";
import rf from "@/services/RequestFactory";

// Singleton ticker manager to prevent duplicate subscriptions
class TickerManager {
  private static instance: TickerManager;
  private subscribers: Set<() => void> = new Set();
  private isSubscribed = false;
  private tickers: { [symbol: string]: Ticker } = {};
  private frameId: number | null = null;
  private socketConnected = false;
  private isInitialized = false;

  static getInstance(): TickerManager {
    if (!TickerManager.instance) {
      TickerManager.instance = new TickerManager();
    }
    return TickerManager.instance;
  }

  async initializeWithApiData(): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log("TickerManager: Initializing with API data");
      const apiTickers = await rf.getRequest("TickerRequest").getTickers();

      if (apiTickers && Array.isArray(apiTickers)) {
        // Convert API response to our Ticker format
        const formattedTickers = apiTickers.map(formatApiTickerToTicker);

        // Update internal tickers state
        formattedTickers.forEach((ticker: Ticker) => {
          this.tickers[ticker.symbol] = ticker;
        });

        this.isInitialized = true;
        console.log(
          `TickerManager: Initialized with ${formattedTickers.length} tickers`
        );

        // Notify all subscribers about the initial data
        this.notifySubscribers();
      }
    } catch (error) {
      console.error(
        "TickerManager: Failed to initialize with API data:",
        error
      );
    }
  }

  private notifySubscribers(): void {
    if (this.frameId !== null) {
      cancelAnimationFrame(this.frameId);
    }

    this.frameId = requestAnimationFrame(() => {
      this.subscribers.forEach((callback) => callback());
      this.frameId = null;
    });
  }

  private handleArrTickersUpdate = (data: TBroadcastEvent) => {
    const tickersUpdated = formatTickersUpdate(
      JSON.parse(data.detail) as TTickerUpdate[]
    );

    // Update tickers
    tickersUpdated.forEach((ticker) => {
      this.tickers[ticker.symbol] = ticker;
    });

    // Batch notify subscribers using requestAnimationFrame
    if (this.frameId !== null) {
      cancelAnimationFrame(this.frameId);
    }

    this.frameId = requestAnimationFrame(() => {
      this.subscribers.forEach((callback) => callback());
      this.frameId = null;
    });
  };

  private doSubscribe() {
    if (!this.isSubscribed && this.socketConnected) {
      console.log("TickerManager: Subscribing to socket");
      this.isSubscribed = true;
      subscribeSocketChannel({
        params: [getArrTickerRoom()],
      });
      AppBroadcast.on(
        BROADCAST_EVENTS.ARR_TICKERS_UPDATED,
        this.handleArrTickersUpdate
      );
    }
  }

  private doUnsubscribe() {
    if (this.isSubscribed) {
      console.log("TickerManager: Unsubscribing from socket");
      this.isSubscribed = false;
      unsubscribeSocketChannel({
        params: [getArrTickerRoom()],
      });
      AppBroadcast.remove(
        BROADCAST_EVENTS.ARR_TICKERS_UPDATED,
        this.handleArrTickersUpdate
      );

      if (this.frameId !== null) {
        cancelAnimationFrame(this.frameId);
        this.frameId = null;
      }
    }
  }

  setSocketConnected(connected: boolean) {
    if (this.socketConnected === connected) return;

    this.socketConnected = connected;

    if (connected && this.subscribers.size > 0) {
      this.doSubscribe();
    } else if (!connected) {
      this.doUnsubscribe();
    }
  }

  subscribe(callback: () => void): () => void {
    this.subscribers.add(callback);

    // Subscribe to socket only if we have socket connection and this is first subscriber
    if (this.subscribers.size === 1 && this.socketConnected) {
      this.doSubscribe();
    }

    // Return unsubscribe function
    return () => {
      this.subscribers.delete(callback);

      // Unsubscribe from socket when no more subscribers
      if (this.subscribers.size === 0) {
        this.doUnsubscribe();
      }
    };
  }

  getTickers(): { [symbol: string]: Ticker } {
    return this.tickers;
  }
}

// Hook for single ticker subscription (for individual pair pages)
export const useSingleTicker = (symbol: string) => {
  const dispatch = useDispatch();
  const { ticker } = useSelector((state: RootState) => state.ticker);
  const socketConnected = useSelector(
    (state: RootState) => state.metadata.socketConnected
  );

  const tickerHandlerRef = useRef<TickerHandler | null>(null);

  const handleTickerUpdate = useCallback((data: TBroadcastEvent) => {
    if (tickerHandlerRef.current) {
      tickerHandlerRef.current.processTickerUpdate(JSON.parse(data.detail));
    }
  }, []);

  // Initialize with API data for the specific symbol
  useEffect(() => {
    if (!symbol) return;

    const initializeSingleTicker = async () => {
      try {
        const apiTicker = await rf
          .getRequest("TickerRequest")
          .getTicker(symbol);
        if (apiTicker) {
          const formattedTicker = formatApiTickerToTicker(apiTicker);
          dispatch(setTicker(formattedTicker));
        }
      } catch (error) {
        console.error(`Failed to initialize ticker for ${symbol}:`, error);
      }
    };

    initializeSingleTicker();
  }, [symbol, dispatch]);

  useEffect(() => {
    if (!symbol || !socketConnected) {
      return;
    }

    // Create ticker handler
    const onUpdate = (marketData: Ticker) => {
      dispatch(setTicker(marketData));
    };
    tickerHandlerRef.current = new TickerHandler(symbol, onUpdate);

    // Subscribe to socket channel
    subscribeSocketChannel({
      params: [getTickerRoom(symbol)],
    });

    // Listen to broadcast events
    AppBroadcast.on(BROADCAST_EVENTS.TICKER_UPDATED, handleTickerUpdate);

    return () => {
      // Cleanup
      unsubscribeSocketChannel({
        params: [getTickerRoom(symbol)],
      });
      AppBroadcast.remove(BROADCAST_EVENTS.TICKER_UPDATED, handleTickerUpdate);
      tickerHandlerRef.current = null;
    };
  }, [symbol, socketConnected, dispatch, handleTickerUpdate]);

  return {
    ticker: ticker,
    isConnected: socketConnected,
  };
};

export const useMultipleTickers = () => {
  const dispatch = useDispatch();
  const { tickers } = useSelector((state: RootState) => state.ticker);
  const socketConnected = useSelector(
    (state: RootState) => state.metadata.socketConnected
  );

  const [renderCount, setRenderCount] = useState(0);

  // Use useRef to get stable reference to ticker manager
  const tickerManagerRef = useRef<TickerManager | null>(null);
  if (!tickerManagerRef.current) {
    tickerManagerRef.current = TickerManager.getInstance();
  }

  // Initialize with API data on first load
  useEffect(() => {
    const initializeData = async () => {
      await tickerManagerRef.current!.initializeWithApiData();
    };

    initializeData();
  }, []); // Run only once on mount

  // Use useRef to create stable callback that doesn't change on every render
  const forceUpdateRef = useRef<(() => void) | null>(null);
  forceUpdateRef.current = () => {
    setRenderCount((prev) => prev + 1);

    // Update Redux store with all tickers
    const allTickers = Object.values(tickerManagerRef.current!.getTickers());
    dispatch(setTickers(allTickers));
  };

  // Notify ticker manager about socket connection changes
  useEffect(() => {
    tickerManagerRef.current!.setSocketConnected(socketConnected);
  }, [socketConnected]);

  // Subscribe to ticker manager (only once, not dependent on socket connection)
  useEffect(() => {
    // Create stable callback wrapper
    const stableCallback = () => {
      forceUpdateRef.current?.();
    };

    // Subscribe to the singleton ticker manager
    const unsubscribe = tickerManagerRef.current!.subscribe(stableCallback);

    return unsubscribe;
  }, []); // No dependencies - subscribe only once

  return {
    tickers: tickerManagerRef.current.getTickers(),
    tickersArray: tickers,
    isConnected: socketConnected,
    renderCount, // For components that need to trigger re-renders
  };
};

// Hook for getting specific ticker data from the multiple tickers cache
export const useTickerData = (symbol: string) => {
  const { tickers, isConnected } = useMultipleTickers();

  // Memoize the ticker to prevent unnecessary re-renders
  const ticker = useMemo(() => {
    return tickers[symbol] || null;
  }, [tickers, symbol]);

  return {
    ticker,
    isConnected,
  };
};
