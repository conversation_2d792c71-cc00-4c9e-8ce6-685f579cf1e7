import { useEffect, useState, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { TAccountUpdateWsData, TBalance } from "@/types/account";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import BigNumber from "bignumber.js";
import { Ticker } from "@/types/pair";
import { useMultipleTickers } from "./useTicker";

const calculateUsdValue = (
  balance: TBalance,
  tickers: { [symbol: string]: Ticker }
): string => {
  const asset = balance.asset?.toUpperCase();

  if (asset === "USDT") {
    return BigNumber(balance.available || 0)
      .plus(balance.locked || 0)
      .decimalPlaces(8, BigNumber.ROUND_DOWN)
      .toFixed();
  }

  const tickerSymbol = `${asset?.toUpperCase()}USDT`;
  const ticker = tickers[tickerSymbol];

  if (!ticker?.lastPrice) {
    return "0";
  }

  const totalBalance = BigNumber(balance.available || 0).plus(
    balance.locked || 0
  );
  return totalBalance
    .multipliedBy(ticker.lastPrice)
    .decimalPlaces(8, BigNumber.ROUND_DOWN)
    .toFixed();
};

const useAccountBalance = ({ coin }: { coin?: string }) => {
  const balances = useSelector(
    (state: RootState) => state.account.account?.balances
  );

  const dispatch = useDispatch();
  const [accountBalances, setAccountBalances] = useState<TBalance[] | []>([]);
  const [coinBalance, setCoinBalance] = useState<TBalance>({
    available: "0",
    locked: "0",
    asset: "",
  });

  // Use the optimized ticker hook for synchronized ticker data
  const { tickers } = useMultipleTickers();

  // Create a memoized key for ticker changes to trigger recalculations
  const tickersKey = useMemo(() => {
    return Object.entries(tickers)
      .map(([symbol, ticker]) => `${symbol}:${ticker.lastPrice}`)
      .sort()
      .join("|");
  }, [JSON.stringify(tickers)]);

  useEffect(() => {
    if (!tickersKey.length) {
      return;
    }

    const currentBalances = balances || [];

    if (coin) {
      const coinBalance = currentBalances.find(
        (balance) => balance.asset?.toLowerCase() === coin?.toLowerCase()
      );

      if (coinBalance?.asset) {
        const usdValue = calculateUsdValue(coinBalance, tickers);
        setCoinBalance({
          ...coinBalance,
          usdValue: usdValue,
        });
      }
    }

    const balanceWithUsdValue = currentBalances.map((balance) => {
      const usdValue = calculateUsdValue(balance, tickers);
      return {
        ...balance,
        usdValue: usdValue,
      };
    });

    setAccountBalances(balanceWithUsdValue);
  }, [balances, coin, tickersKey]);

  useEffect(() => {
    const handleAccountUpdate = (event: TBroadcastEvent) => {
      const balanceUpdated: TAccountUpdateWsData = JSON.parse(event.detail);

      const currentBalances = balances || [];
      const assetExists = currentBalances.some(
        (item) =>
          item.asset?.toUpperCase() === balanceUpdated.asset?.toUpperCase()
      );

      let accountBalancesUpdated;

      if (assetExists) {
        accountBalancesUpdated = currentBalances.map((item) => {
          const isSameAsset =
            item.asset?.toUpperCase() === balanceUpdated.asset?.toUpperCase();
          const hasValidOperationId =
            !item.operationId || balanceUpdated.operationId > item.operationId;
          const shouldUpdateBalance = isSameAsset && hasValidOperationId;

          if (shouldUpdateBalance) {
            const updatedBalance = {
              ...item,
              available: balanceUpdated.available,
              locked: balanceUpdated.locked,
              operationId: balanceUpdated.operationId,
            };

            const usdValue = calculateUsdValue(updatedBalance, tickers);
            return {
              ...updatedBalance,
              usdValue,
            };
          }
          return item;
        });
      } else {
        const newBalance = {
          asset: balanceUpdated.asset,
          available: balanceUpdated.available,
          locked: balanceUpdated.locked,
          operationId: balanceUpdated.operationId,
        };

        const usdValue = calculateUsdValue(newBalance, tickers);

        accountBalancesUpdated = [
          ...currentBalances,
          {
            ...newBalance,
            usdValue,
          },
        ];
      }

      setAccountBalances(accountBalancesUpdated);
    };

    AppBroadcast.on(BROADCAST_EVENTS.ACCOUNT_UPDATED, handleAccountUpdate);

    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.ACCOUNT_UPDATED,
        handleAccountUpdate
      );
    };
  }, [balances, dispatch, tickers]);

  // Recalculate USD values when tickers change
  useEffect(() => {
    if (!Object.keys(tickers).length) {
      return;
    }

    setAccountBalances((prevBalances) => {
      return prevBalances.map((item) => ({
        ...item,
        usdValue: calculateUsdValue(item, tickers),
      }));
    });

    // Also update coin balance if it exists
    if (coin) {
      setCoinBalance((prevCoinBalance) => {
        if (prevCoinBalance.asset) {
          return {
            ...prevCoinBalance,
            usdValue: calculateUsdValue(prevCoinBalance, tickers),
          };
        }
        return prevCoinBalance;
      });
    }
  }, [tickersKey, coin]);

  return {
    accountBalances,
    coinBalance,
  };
};

export default useAccountBalance;
