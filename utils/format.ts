import { SI } from "@/constants";
import BigNumber from "bignumber.js";
import commaNumber from "comma-number";
import { startsWith } from "lodash";
import moment from "moment";
import { TOrderUpdated } from "@/types/order";
import { isEmpty } from "lodash";
import { TTradeMarketUpdated, TTradeUpdated, TUserTrade } from "@/types/trade";
import { EOrderSide } from "@/components/OrderForm/OrderFormMobile";
import { TTradeRecent } from "@/components/RecentTrade/services/RecentTradesHandler";
import { TTickerUpdate } from "@/components/PairMarket/services/TickerHandler";
import { TTickerResponse } from "@/types/pair";

export const formatUnixTimestamp = (
  timestamp: number,
  formatDate = "YYYY-MM-DD HH:mm:ss"
) => {
  if (!timestamp) return "--";

  return moment(+timestamp).format(formatDate);
};

export const formatAgeTime = (
  timestamp: number,
  suffix: string = "",
  prefix: string = ""
) => {
  const duration = moment.duration(
    moment().diff(moment(timestamp)),
    "milliseconds"
  );
  const days = Math.floor(duration.asDays());
  const hours = Math.floor(duration.asHours());
  const minutes = Math.floor(duration.asMinutes());
  const seconds = Math.floor(duration.asSeconds());

  if (days >= 1) {
    return `${prefix} ${days}d ${suffix}`;
  }

  if (hours >= 1) {
    return `${prefix} ${hours}h ${suffix}`;
  }

  if (minutes >= 1) {
    return `${prefix} ${minutes}m ${suffix}`;
  }

  if (seconds >= 1) {
    return `${prefix} ${seconds}s ${suffix}`;
  }

  return "--";
};

export const covertMistToDec = (
  amount: string | number | undefined | BigNumber,
  decimals = 9
) => {
  return new BigNumber(amount || 0).dividedBy(10 ** decimals).toString();
};

export const convertDecToMist = (
  amount: string | number | undefined | BigNumber,
  decimals = 9
) => {
  return new BigNumber(amount || 0).multipliedBy(10 ** decimals).toString();
};

export const truncateDecimals = (value: number, decimals = 9) => {
  return Math.round(value * 10 ** decimals) / 10 ** decimals;
};

export const formatShortAddress = (
  address: string | undefined,
  digits = 8,
  digitsAfter = 6
): string => {
  if (!address) {
    return "--";
  }
  return `${address?.substring(0, digits)}...${address.substring(
    address.length - digitsAfter,
    address.length
  )}`;
};

export const formatMillisecondsToDate = (milliseconds: number): string => {
  const date = new Date(milliseconds);
  return date.toLocaleString("en-US", {
    month: "2-digit",
    day: "2-digit",
    year: "numeric",
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true,
  });
};

export const getCoinAddressFromType = (coinType: string) => {
  return coinType.split("::")[0];
};

export const formatTokenAddress = (tokenAddress: string) => {
  if (startsWith(tokenAddress, "0x")) return tokenAddress;
  return "0x" + tokenAddress;
};
export function formatCommentTime(createdAt: string): string {
  const now = new Date();
  const createdDate = new Date(createdAt);
  const diffInSeconds = Math.max(
    0,
    Math.floor((now.getTime() - createdDate.getTime()) / 1000)
  );

  if (diffInSeconds < 60) {
    return `${diffInSeconds}s`;
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes}m`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours}h`;
  }

  const options: Intl.DateTimeFormatOptions = {
    month: "short",
    day: "numeric",
  };
  return createdDate.toLocaleDateString("en-US", options);
}

export const formatNumberWithComa = (num: number | string, decimals = 8) => {
  const number = Number(num);
  if (isNaN(number)) {
    return "--";
  }

  if (Number.isInteger(number)) {
    return number.toLocaleString();
  }

  const fixedNumber = number.toFixed(decimals);
  const trimmedNumber = parseFloat(fixedNumber);

  const [integerPart, decimalPart] = trimmedNumber.toString().split(".");

  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ",");

  return decimals > 0 ? `${formattedInteger}.${decimalPart}` : formattedInteger;
};

export const formatMillisecondsToFullDate = (milliseconds: number): string => {
  if (!milliseconds) return "--";
  const date = new Date(milliseconds);

  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const year = date.getFullYear();

  return `${hours}:${minutes}:${seconds} ${day}/${month}/${year}`;
};

export const formatCurrency = (value: string | undefined): string => {
  if (!value || value === "NaN") {
    return "--";
  }
  const numberValue = parseFloat(value);
  return `$${numberValue.toLocaleString("en-US", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;
};

export function formatToPercent(
  number: string | number | BigNumber,
  decimalPlaces = 2,
  defaultValue = "0%"
): string {
  if (
    !number ||
    new BigNumber(number || 0).isZero() ||
    !new BigNumber(number).isFinite()
  ) {
    return defaultValue;
  }
  const newValue = new BigNumber(number)
    .multipliedBy(100)
    .toFixed(decimalPlaces);
  return new BigNumber(newValue).toString() + "%";
}

export function formatNumber(
  value: string | number | BigNumber,
  decimalPlaces = 8,
  isFormatLargeNumber = true,
  defaultValue = "--"
): string {
  if (!value || BigNumber(value || 0).isZero()) {
    return defaultValue;
  }

  if (
    BigNumber(value).isGreaterThan(0) &&
    BigNumber(value).isLessThan(0.00000001)
  ) {
    return "<" + new BigNumber(0.00000001).toString();
  }

  if (!isFormatLargeNumber) {
    return formatNumberWithCommas(value, decimalPlaces);
  }

  return _formatLargeNumberIfNeed(
    roundNumber(value, BigNumber.ROUND_DOWN, decimalPlaces),
    decimalPlaces
  );
}

const _formatLargeNumberIfNeed = (number: string, digits = 0) => {
  const comparedResult = BigNumber(number).comparedTo(1000);
  if (comparedResult && comparedResult < 0) {
    return formatNumberWithCommas(number, digits);
  }
  const rx = /\.0+$|(\.[0-9]*[1-9])0+$/;
  const num = parseFloat(number);
  let i;
  for (i = SI.length - 1; i > 0; i--) {
    if (num >= SI[i].value) {
      break;
    }
  }
  return (
    BigNumber(num / SI[i].value)
      .toFixed(digits, BigNumber.ROUND_DOWN)
      .toString()
      .replace(rx, "$1") + SI[i].symbol
  );
};

export const roundNumber = (
  number: number | string | BigNumber,
  roundMode = BigNumber.ROUND_DOWN,
  decimals = 18
) => {
  const newNumber = new BigNumber(number).toFixed(decimals, roundMode);
  return new BigNumber(newNumber).toString();
};

export const formatPrice = (
  value: number | string,
  decimals: number = 2
): string => {
  // Convert string to number if needed
  const numValue = typeof value === "string" ? parseFloat(value) : value;

  // Handle invalid inputs
  if (numValue === null || numValue === undefined || isNaN(numValue)) {
    return "0.00";
  }

  // Format with fixed decimal places
  const fixedValue = numValue.toFixed(decimals);

  // Split into integer and decimal parts
  const parts = fixedValue.split(".");
  const integerPart = parts[0];
  const decimalPart = parts[1];

  // Add comma separators to integer part
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ",");

  // Combine parts
  return decimalPart ? `${formattedInteger}.${decimalPart}` : formattedInteger;
};

export function formatNumberWithCommas(
  value: string | number | BigNumber,
  decimalPlaces = 8
): string {
  return commaNumber(
    new BigNumber(Number(value).toFixed(decimalPlaces)).toString(),
    ",",
    "."
  );
}

export function formatUsdNumber(
  value: string | number | BigNumber,
  defaultValue = "--"
): string {
  if (!value || new BigNumber(value || 0).isZero()) {
    return defaultValue;
  }

  if (
    new BigNumber(value).isGreaterThan(0) &&
    new BigNumber(value).isLessThan(0.00000001)
  ) {
    return "<$" + new BigNumber(0.00000001).toString();
  }

  if (new BigNumber(value).isGreaterThan(1)) {
    return formatNumber(value, 2);
  }

  const numStr = new BigNumber(value).toExponential();
  const exponent = parseInt(numStr.split("e-")[1]) || 1;

  return formatNumber(value, exponent - 1 + 4);
}

export const maskEmail = (email: string) => {
  const [localPart, domain] = email.split("@");

  if (localPart.length <= 4) {
    return localPart[0] + "****" + "@" + domain;
  }

  const firstTwo = localPart.slice(0, 2);
  const lastTwo = localPart.slice(-2);

  return `${firstTwo}****${lastTwo}@${domain}`;
};

export const formatOrderUpdated = (data: TOrderUpdated) => {
  if (isEmpty(data)) {
    return {};
  }
  return {
    order_id: data.i,
    user_id: data.u,
    symbol: data.s,
    side: data.S === 0 ? EOrderSide.BUY : EOrderSide.SELL,
    type: data.o,
    price: data.p,
    orig_qty: data.q,
    executed_qty: data.Y,
    cummulative_quote_qty: data.Z,
    status: data.A,
    created_at: data.M,
  };
};

export const formatTradeUpdated = (data: TTradeUpdated): TUserTrade | null => {
  if (isEmpty(data)) {
    return null;
  }

  return {
    orderId: data.o,
    id: data.i,
    symbol: data.s,
    isBuyer: data.M,
    isMaker: data.m,
    price: data.p,
    qty: data.q,
    quoteQty: data.Q,
    fee: data.f,
    time: data.T,
  };
};
export const formatMarketTradeRecent = (
  data: TTradeMarketUpdated
): TTradeRecent | null => {
  if (isEmpty(data)) {
    return null;
  }

  return {
    id: data.a,
    symbol: data.s,
    isBuyerMaker: data.m,
    price: data.p,
    quantity: data.q,
    time: data.T,
  };
};

export const formatTickersUpdate = (data: TTickerUpdate[]) => {
  return data.map((item) => ({
    symbol: item.s,
    lastPrice: item.c,
    highPrice: item.h,
    lowPrice: item.l,
    priceChangePercent: item.P,
    priceChange: item.p,
    baseVolume: item.v,
    quoteVolume: item.q,
    timestamp: item.E,
    isUp: item.p.startsWith("-") ? false : true,
    isUp24h: item.P.startsWith("-") ? false : true,
  }));
};

export const formatApiTickerToTicker = (apiTicker: TTickerResponse) => {
  return {
    symbol: apiTicker.symbol,
    lastPrice: apiTicker.lastPrice,
    highPrice: apiTicker.highPrice,
    lowPrice: apiTicker.lowPrice,
    priceChangePercent: apiTicker.priceChangePercent,
    priceChange: apiTicker.priceChange,
    baseVolume: apiTicker.baseVolume,
    quoteVolume: apiTicker.quoteVolume,
    timestamp: Date.now(), // Use current timestamp since API doesn't provide it
    isUp: parseFloat(apiTicker.priceChangePercent || "0") >= 0,
    isUp24h: parseFloat(apiTicker.priceChangePercent || "0") >= 0,
    latestChange: "0",
  };
};
