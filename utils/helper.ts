import BigNumber from "bignumber.js";
import { errorMsg, successMsg } from "@/libs/toast";
import copy from "copy-to-clipboard";
import { EOrderSide } from "@/components/OrderForm/OrderFormMobile";
import { isZero } from "./number";

export const copyToClipboard = (message: string) => {
  try {
    copy(message);
    successMsg("Copied!");
  } catch (error: any) {
    errorMsg(error.message || "Something went wrong!");
  }
};

export const getDecimalPlaces = (value: string | number): number => {
  const bn = new BigNumber(value);
  return bn.decimalPlaces() ?? 0;
};
export const isMobile = () => {
  const isBrowser = typeof window !== "undefined";
  return (
    isBrowser &&
    typeof navigator !== "undefined" &&
    /Mobi|Android|iPhone|iPad|iPod/i.test(navigator.userAgent)
  );
};

export const getPriceStyle = (priceChange: string | null) => {
  if (!priceChange || isZero(priceChange)) return "var(--color-white-900)";

  if (BigNumber(priceChange).isGreaterThan(0)) {
    return "var(--color-green-500)";
  }

  return "var(--color-red-400)";
};

export const getSideColor = (orderSide: EOrderSide) => {
  if (orderSide?.toUpperCase() == EOrderSide.BUY) {
    return "var(--color-green-600)";
  }

  return "var(--color-red-400)";
};

export const sleep = (ms: number) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export const removeCommas = (numStr: string) => {
  return numStr.replace(/,/g, "");
};

export const calculateFilledPercent = (
  filled: string | null,
  total: string
) => {
  if (!filled || isZero(filled)) return 0;

  return BigNumber(filled).dividedBy(total).multipliedBy(100).toFixed(2);
};

export const isInvalidNumber = (value: string | number) => {
  return !value || isNaN(Number(value));
};

export const filterParams = (params: any) => {
  return Object.fromEntries(Object.entries(params).filter(([_, v]) => v));
};

export const isValidEmail = (email: string) => {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
};
