import { Ticker } from "@/types/pair";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

const defaultTicker: Ticker = {
  lastPrice: null,
  highPrice: null,
  lowPrice: null,
  priceChangePercent: null,
  priceChange: null,
  baseVolume: null,
  quoteVolume: null,
  timestamp: null,
  isUp: false,
  isUp24h: false,
  symbol: "",
};

interface TickerState {
  ticker: Ticker;
  tickers: Ticker[];
}

const initialState: TickerState = {
  ticker: defaultTicker,
  tickers: [],
};

export const tickerSlice = createSlice({
  name: "ticker",
  initialState,
  reducers: {
    setTicker: (state, action: PayloadAction<Ticker>) => {
      state.ticker = action.payload;
    },
    setTickers: (state, action: PayloadAction<Ticker[]>) => {
      state.tickers = action.payload;
    },
  },
});

export const { setTicker, setTickers } = tickerSlice.actions;

export default tickerSlice.reducer;
