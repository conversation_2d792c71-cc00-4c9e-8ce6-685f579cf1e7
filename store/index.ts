import { combineReducers, configureStore } from "@reduxjs/toolkit";
import user from "./user.store";
import ticker from "./ticker.store";
import account from "./account.store";
import metadata from "./metadata.store";
import favorites from "./favorites.store";
import pairSettings from "./pairSettings.store";

const rootReducer = combineReducers({
  user,
  ticker,
  account,
  metadata,
  favorites,
  pairSettings,
});

const store = configureStore({
  reducer: rootReducer,
});

export type RootState = ReturnType<typeof store.getState>;

export { store };
