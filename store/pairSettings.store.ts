import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { TPairSetting, ETradingStatus } from "@/types/pair";

export interface PairSettingsState {
  pairSettings: TPairSetting[];
  pairSettingsMap: { [symbol: string]: TPairSetting };
  activePairSettings: TPairSetting[];
}

const initialState: PairSettingsState = {
  pairSettings: [],
  pairSettingsMap: {},
  activePairSettings: [],
};

export const pairSettingsSlice = createSlice({
  name: "pairSettings",
  initialState,
  reducers: {
    setPairSettings: (state, action: PayloadAction<TPairSetting[]>) => {
      state.pairSettings = action.payload;

      // Create a map for quick lookups
      state.pairSettingsMap = {};
      action.payload.forEach((setting) => {
        state.pairSettingsMap[setting.symbol.toUpperCase()] = setting;
      });

      // Filter active pairs
      state.activePairSettings = action.payload.filter(
        (setting) => setting.status === ETradingStatus.ACTIVE
      );
    },
  },
});

export const { setPairSettings } = pairSettingsSlice.actions;

export default pairSettingsSlice.reducer;
