"use client";

import React, { useEffect, useState } from "react";
import ReactModal from "react-modal";
import { EOrderType } from "../components/OrderForm/index";
import { EOrderSide } from "../components/OrderForm/OrderFormMobile";
import { BuyOrderForm } from "@/components/OrderForm/simple-order/BuyOrderForm";
import { SellOrderForm } from "@/components/OrderForm/simple-order/SellOrderForm";

interface Props {
  isOpen: boolean;
  side: EOrderSide;
  onClose?: VoidFunction;
}

export const ModalOrderForm = ({ side, isOpen, onClose }: Props) => {
  const customStyles = {
    content: {
      top: "auto",
      bottom: "0",
      left: "0",
      right: "0",
      borderRadius: "16px 16px 0 0",
      padding: 0,
      background: "#212225",
      overflow: "inherit",
      border: "1px solid rgba(255, 255, 255, 0.1)",
      boxShadow: "4px 4px 8px 0px rgba(8, 9, 12, 0.50)",
    },
    overlay: {
      background: "rgba(8, 9, 12, 0.60)",
      zIndex: 999,
    },
  };

  const [orderType, setOrderType] = useState<EOrderType>(EOrderType.LIMIT);
  const [orderSide, setOrderSide] = useState<EOrderSide>(EOrderSide.BUY);

  useEffect(() => {
    document.body.style.overflow = isOpen ? "hidden" : "auto";
    return () => {
      document.body.style.overflow = "auto";
    };
  }, [isOpen]);

  useEffect(() => {
    setOrderSide(side);
  }, [side]);

  return (
    <ReactModal
      isOpen={isOpen}
      onRequestClose={onClose}
      style={customStyles}
      ariaHideApp={false}
      bodyOpenClassName="overflow-hidden"
    >
      <div className={`relative w-[calc(100vw)] px-4 py-6`}>
        <div className="bg-white-100 mb-2.5 grid grid-cols-2 rounded-[4px] p-[2px]">
          <div
            className="flex w-full"
            onClick={() => setOrderSide(EOrderSide.BUY)}
          >
            <div
              className={`body-sm-semibold-12 flex flex-1 items-center justify-center rounded-l-[4px] text-center ${
                orderSide === EOrderSide.BUY ? "bg-green-500" : ""
              } `}
            >
              Buy
            </div>
            {orderSide === EOrderSide.BUY && (
              <div className="border-y-12 h-0 w-0 border-l-[12px] border-y-transparent border-l-green-500" />
            )}
          </div>
          <div
            className="flex w-full"
            onClick={() => setOrderSide(EOrderSide.SELL)}
          >
            {orderSide === EOrderSide.SELL && (
              <div className="border-y-12 h-0 w-0 border-r-[12px] border-y-transparent border-r-red-500" />
            )}

            <div
              className={`body-sm-semibold-12 flex flex-1 items-center justify-center rounded-r-[4px] text-center ${
                orderSide === EOrderSide.SELL ? "bg-red-500" : ""
              } `}
            >
              Sell
            </div>
          </div>
        </div>
        <div className="body-sm-semibold-12 border-white-100 mb-6 grid grid-cols-2 border-b">
          <div
            onClick={() => setOrderType(EOrderType.LIMIT)}
            className={`cursor-pointer px-4 py-2.5 text-center ${
              orderType === EOrderType.LIMIT
                ? "border-white-500 border-b"
                : "text-white-500"
            }`}
          >
            Limit
          </div>
          <div
            onClick={() => setOrderType(EOrderType.MARKET)}
            className={`cursor-pointer px-4 py-2.5 text-center ${
              orderType === EOrderType.MARKET
                ? "border-white-500 border-b"
                : "text-white-500"
            }`}
          >
            Market
          </div>
        </div>
        {orderSide === EOrderSide.BUY ? (
          <BuyOrderForm orderType={orderType} />
        ) : (
          <SellOrderForm orderType={orderType} />
        )}
      </div>
    </ReactModal>
  );
};
