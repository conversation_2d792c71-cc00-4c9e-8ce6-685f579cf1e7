import config from "@/config";
import BaseRootRequest from "./BaseRequest";
import { TTickerResponse } from "@/types/pair";

export default class TickerRequest extends BaseRootRequest {
  getUrlPrefix() {
    return `${config.apiUrl}/v1`;
  }

  getTicker(symbol: string): Promise<TTickerResponse> {
    const url = `/ticker/24hr`;
    return this.get(url, { symbol });
  }

  async getTickers(): Promise<TTickerResponse[]> {
    const url = `/tickers/24hr`;
    return this.get(url);
  }
}
