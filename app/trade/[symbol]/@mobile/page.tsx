"use client";

import React, { useCallback, useEffect, useState } from "react";
import OrderFormMobile from "@/components/OrderForm/OrderFormMobile";
import Link from "next/link";
import { HistoryIcon } from "@/assets/icons";
import { CoinAbout } from "@/components/CoinAbout";
import TradingView from "@/components/TradingView";
import { OrderBook } from "@/components/OrderBook";
import { PairTicker } from "@/components/PairTicker";
import { TableOpenOrder } from "@/components/OpenOrder";
import { TableMarketTrades } from "@/components/RecentTrade/TableMarketTrades";
import { usePairContext } from "../provider";
import { useParams } from "next/navigation";

enum EMainSectionTabKeys {
  CHART = "chart",
  ORDER_BOOK = "order-book",
  TRADES = "trades",
  INFO = "Info",
}

const MAIN_SECTION_TABS = [
  {
    label: "Chart",
    key: EMainSectionTabKeys.CHART,
  },
  {
    label: "Order Book",
    key: EMainSectionTabKeys.ORDER_BOOK,
  },
  {
    label: "Trades",
    key: EMainSectionTabKeys.TRADES,
  },
  {
    label: "Info",
    key: EMainSectionTabKeys.INFO,
  },
];

enum EBottomSectionTabKeys {
  OPEN_ORDER = "open-order",
  HOLDINGS = "holding",
}

const BOTTOM_SECTION_TABS = [
  {
    label: "Open Order",
    key: EBottomSectionTabKeys.OPEN_ORDER,
  },
  {
    label: "Holding",
    key: EBottomSectionTabKeys.HOLDINGS,
  },
];

export default function MobileTradePage() {
  const [mainSectionTab, setMainSectionTab] = useState<EMainSectionTabKeys>(
    EMainSectionTabKeys.CHART
  );
  const [bottomSectionTab, setBottomSectionTab] =
    useState<EBottomSectionTabKeys>(EBottomSectionTabKeys.OPEN_ORDER);
  const { pairSetting } = usePairContext();
  const [isClient, setIsClient] = useState(false);

  const renderMainSectionContent = useCallback(() => {
    if (mainSectionTab === EMainSectionTabKeys.CHART) {
      return (
        <div className="h-[476px] w-full lg:h-[400px]">
          <TradingView symbol={pairSetting?.symbol as string} />
        </div>
      );
    }
    if (mainSectionTab === EMainSectionTabKeys.ORDER_BOOK) {
      return (
        <div className="mb-2">
          <OrderBook />
        </div>
      );
    }
    if (mainSectionTab === EMainSectionTabKeys.TRADES) {
      return (
        <div className="mb-2 px-2">
          <TableMarketTrades pairSetting={pairSetting} />
        </div>
      );
    }
    if (mainSectionTab === EMainSectionTabKeys.INFO) {
      return (
        <div>
          <CoinAbout />
        </div>
      );
    }
    return <></>;
  }, [mainSectionTab]);

  const renderBottomSectionContent = useCallback(() => {
    if (bottomSectionTab === EBottomSectionTabKeys.OPEN_ORDER) {
      return (
        <div>
          <TableOpenOrder isInPair={true} />
        </div>
      );
    }
    if (bottomSectionTab === EBottomSectionTabKeys.HOLDINGS) {
      return <div>HOLDINGS</div>;
    }
    return <></>;
  }, [bottomSectionTab]);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return <></>;
  }

  return (
    <div className="bg-black-900 min-h-[calc(100vh-50px)]">
      <div className="pb-[72px]">
        <PairTicker />
        <div className="border-white-50 flex border-b">
          {MAIN_SECTION_TABS.map((item, index) => {
            return (
              <div
                onClick={() => setMainSectionTab(item.key)}
                className={`cursor-pointer px-3 py-2.5 ${
                  item.key === mainSectionTab
                    ? "text-white-1000 body-sm-medium-12 border-white-500 border-b"
                    : "text-white-500 body-sm-regular-12"
                }`}
                key={index}
              >
                {item.label}
              </div>
            );
          })}
        </div>
        <div>{renderMainSectionContent()}</div>

        <div className="border-white-50 border-t">
          <div>
            <div className="border-white-50 flex items-center justify-between gap-2 border-b pr-4">
              <div className="flex">
                {BOTTOM_SECTION_TABS.map((item, index) => {
                  return (
                    <div
                      onClick={() => setBottomSectionTab(item.key)}
                      className={`cursor-pointer px-3 py-2.5 ${
                        item.key === bottomSectionTab
                          ? "text-white-1000 body-sm-medium-12 border-white-500 border-b"
                          : "text-white-500 body-sm-regular-12"
                      }`}
                      key={index}
                    >
                      {item.label}
                    </div>
                  );
                })}
              </div>
              <Link href="/my/orders-exchange">
                <HistoryIcon />
              </Link>
            </div>

            <div>{renderBottomSectionContent()}</div>
          </div>{" "}
        </div>
      </div>

      <OrderFormMobile />
    </div>
  );
}
