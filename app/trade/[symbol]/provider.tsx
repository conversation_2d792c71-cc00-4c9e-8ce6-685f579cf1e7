"use client";
import TickerHandler from "@/components/PairMarket/services/TickerHandler";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import { setTicker } from "@/store/ticker.store";
import { PairSetting, Ticker } from "@/types/pair";
import React, { useContext, createContext, useEffect } from "react";
import { useDispatch } from "react-redux";

type PairPageContextProps = {
  symbol: string;
  pairSetting: PairSetting | null;
};

const PairPageContext = createContext<PairPageContextProps>({
  symbol: "",
  pairSetting: null,
});

const PairProvider: React.FC<{
  children: React.ReactNode;
  pairSetting: PairSetting | null;
  symbol: string;
}> = ({ children, pairSetting, symbol }) => {
  const dispatch = useDispatch();

  // We are subscribing to ticker updates in PairTicker
  useEffect(() => {
    const onUpdate = (marketData: Ticker) => {
      dispatch(setTicker(marketData));
    };
    const tickerHandler = new TickerHandler(symbol, onUpdate);

    const handleTickerUpdate = (data: TBroadcastEvent) => {
      tickerHandler.processTickerUpdate(JSON.parse(data.detail));
    };

    AppBroadcast.on(BROADCAST_EVENTS.TICKER_UPDATED, handleTickerUpdate);

    return () => {
      AppBroadcast.remove(BROADCAST_EVENTS.TICKER_UPDATED, handleTickerUpdate);
    };
  }, [dispatch, symbol]);

  const contextValue = {
    symbol,
    pairSetting,
  };

  return (
    <PairPageContext.Provider value={contextValue}>
      {children}
    </PairPageContext.Provider>
  );
};

export const usePairContext = () => useContext(PairPageContext);

export { PairProvider };
