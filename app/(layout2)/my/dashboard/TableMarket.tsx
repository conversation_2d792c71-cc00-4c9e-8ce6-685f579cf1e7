"use client";

import React, { memo, useEffect, useState, useMemo } from "react";
import { AppButtonSort, AppButton } from "@/components";
import { AppNumber } from "@/components/AppNumber";
import { useMediaQuery } from "react-responsive";
import Link from "next/link";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";
import { TAsset } from "@/types/asset";
import _ from "lodash";
import useAccountBalance from "@/hooks/useAccountBalance";
import { TBalance } from "@/types/account";
import { getPriceStyle } from "@/utils/helper";

type TEnhancedAsset = TAsset & {
  available: string;
  usdValue: string;
  price?: string;
  change24h: string;
};

const SORT_CONFIGS = {
  symbol: {
    field: "symbol",
    type: "string" as const,
    transform: (value: any) => value?.toLowerCase() || "",
  },
  usdValue: {
    field: "usdValue",
    type: "number" as const,
    transform: (value: any) => Number(value || 0),
  },
  price: {
    field: "price",
    type: "number" as const,
    transform: (value: any) => Number(value || 0),
  },
  change24h: {
    field: "change24h",
    type: "number" as const,
    transform: (value: any) => Number(value || 0),
  },
} as const;

const sortTokens = (
  tokens: TEnhancedAsset[],
  sortBy: string,
  sortType: string
): TEnhancedAsset[] => {
  if (!sortBy || !sortType) return tokens;

  const config = SORT_CONFIGS[sortBy as keyof typeof SORT_CONFIGS];
  if (!config) return tokens;

  return _.orderBy(
    tokens,
    [
      (item: TEnhancedAsset) =>
        config.transform(item[config.field as keyof TEnhancedAsset]),
    ],
    [sortType as "asc" | "desc"]
  );
};

const CoinItem = ({ coin }: { coin: TEnhancedAsset }) => {
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  return (
    <div className="border-white-50 hover:bg-white-50 flex w-full items-center lg:border-b">
      <div className="w-[40%] px-2 py-2.5 text-left lg:w-[20%] lg:min-w-[184px]">
        <div className="flex flex-col justify-end">
          <div className="body-sm-regular-12">{coin.symbol}</div>
          <div className="body-xs-regular-10 text-white-500">{coin.name}</div>
        </div>
      </div>

      <div className="body-sm-regular-12 hidden w-[30%] px-2 py-2.5 lg:block lg:w-[20%] lg:min-w-[184px]">
        <div className="flex w-full flex-col items-end">
          <div className="body-sm-regular-12">
            <AppNumber value={coin.available} isFormatLargeNumber={false} />
          </div>
          <div className="body-xs-regular-10 text-white-500">
            <AppNumber
              isForUSD
              value={coin.usdValue}
              isFormatLargeNumber={false}
            />
          </div>
        </div>
      </div>

      <div className="body-sm-regular-12 w-[30%] px-2 py-2.5 lg:w-[20%] lg:min-w-[184px]">
        <div className="flex w-full flex-col items-end">
          <div className="body-sm-regular-12">
            <AppNumber
              value={coin.price}
              isFormatLargeNumber={false}
              isForUSD
            />
          </div>
          {/* <div className="body-xs-regular-10 text-white-500">--</div> */}
        </div>
      </div>

      <div className="body-sm-regular-12 w-[30%] px-2 py-2.5 lg:w-[20%] lg:min-w-[184px]">
        {isMobile ? (
          <div className="text-right">
            <Link
              href={`/trade/${coin.tradingPair?.toLocaleLowerCase()}`}
              className="body-sm-medium-12 underline"
            >
              Buy
            </Link>
          </div>
        ) : (
          <div
            className="text-right"
            style={{
              color: getPriceStyle(coin.change24h || "0"),
            }}
          >
            <div className="flex justify-end">
              <AppNumber
                value={coin?.change24h || 0}
                isFormatLargeNumber={false}
              />{" "}
              %
            </div>
          </div>
        )}
      </div>
      <div className="hidden w-[20%] justify-end px-2 py-2.5 md:min-w-[184px] lg:flex">
        <Link
          href={`/trade/${coin.tradingPair?.toLocaleLowerCase()}`}
          className="body-sm-medium-12 underline"
        >
          Trade
        </Link>
      </div>
    </div>
  );
};

CoinItem.displayName = "CoinItem";

export const TableMarkets = memo(() => {
  const [sortBy, setSortBy] = useState<string>("");
  const [sortType, setSortType] = useState<string>("");
  const [isMounted, setIsMounted] = useState<boolean>(false);
  const assets = useSelector((state: RootState) => state.metadata.assets);
  const [tokensShow, setTokensShow] = useState<TEnhancedAsset[]>([]);
  const { accountBalances } = useAccountBalance({});
  const tickers = useSelector((state: RootState) => state.ticker.tickers);
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  // Create tickers key for optimization
  const tickersKey = useMemo(() => {
    return tickers
      .map((ticker) => `${ticker.symbol}:${ticker.lastPrice}`)
      .sort()
      .join("|");
  }, [tickers]);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    // Transform assets to enhanced assets with additional fields
    let dataToken: TEnhancedAsset[] = assets.map((a: TAsset) => {
      const coinAvailable = accountBalances.find(
        (c: TBalance) => c.asset === a.symbol
      );
      const coinPrice = tickers.find(
        (t) => t.symbol?.toUpperCase() === `${a.symbol?.toUpperCase()}USDT`
      );
      return {
        ...a,
        available: coinAvailable?.available || "0",
        usdValue: coinAvailable?.usdValue || "0",
        price:
          a.symbol === "USDT" ? "1" : coinPrice?.lastPrice?.toString() || "0",
        change24h: coinPrice?.priceChangePercent || "0",
        tradingPair:
          a.symbol === "USDT" ? "btcusdt" : `${a.symbol?.toLowerCase()}usdt`,
      };
    });

    // Apply sorting using the refactored sort function
    dataToken = sortTokens(dataToken, sortBy, sortType);

    setTokensShow(dataToken);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [accountBalances, assets, sortBy, sortType, tickersKey]);

  if (!isMounted) return <></>;

  return (
    <>
      <div className="w-full">
        <div className="flex w-full items-center md:hidden">
          <div className="body-sm-regular-12 text-white-500 flex w-[40%] items-center px-2 py-1.5 ">
            Name
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[30%] px-2 py-1.5 text-left ">
            <div className="flex items-center justify-end">Last price</div>
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[30%] px-2 py-1.5 text-left">
            <div className="flex items-center justify-end gap-2">
              {isMobile ? "" : "Change (%)"}
            </div>
          </div>
        </div>

        <div className="hidden w-full items-center md:flex">
          <div className="body-sm-regular-12 text-white-500 flex w-[20%] items-center px-2 py-1.5 md:min-w-[184px] ">
            <div className="flex items-center gap-2">
              Coin
              <AppButtonSort
                value="symbol"
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[20%] px-2 py-1.5 text-left md:min-w-[184px]">
            <div className="flex items-center justify-end gap-2">
              Amount / USD Value
              <AppButtonSort
                value="usdValue"
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[20%] px-2 py-1.5 text-left md:min-w-[184px]">
            <div className="flex items-center justify-end gap-2">
              Price
              <AppButtonSort
                value="price"
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[20%] px-2 py-1.5 text-left md:min-w-[184px]">
            <div className="flex items-center justify-end gap-2">
              24h Change
              <AppButtonSort
                value="change24h"
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[20%] px-2 py-1.5 text-right md:min-w-[184px]">
            Trade
          </div>
        </div>

        <div>
          {tokensShow.map((item: TEnhancedAsset, index: number) => {
            return <CoinItem key={index} coin={item} />;
          })}
        </div>
      </div>
    </>
  );
});

TableMarkets.displayName = "TableMarkets";
