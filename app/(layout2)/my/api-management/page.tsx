"use client";

import React, { useEffect, useState } from "react";
import { AppButton } from "@/components";
import { ChevronDownIcon, CheckboxCheckedIcon } from "@/assets/icons";
import { APIItem } from "./components/APIItem";
import { useMediaQuery } from "react-responsive";
import {
  ModalCreateAPI,
  ModalConfirmDefaultSecurityControls,
  ModalChooseAPIKeyType,
} from "@/modals";

const ApiManagementPage = () => {
  const [isShowFullSecurityControls, setIsShowFullSecurityControls] =
    useState<boolean>(false);
  const [
    isShowModalConfirmDefaultSecurityControls,
    setIsShowModalConfirmDefaultSecurityControls,
  ] = useState<boolean>(false);
  const [isShowModalCreateAPI, setIsShowModalCreateAPI] =
    useState<boolean>(false);
  const [isShowModalChooseType, setIsShowModalChooseType] =
    useState<boolean>(false);
  const [isMounted, setIsMounted] = useState<boolean>(false);

  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) return <></>;

  return (
    <div className="px-4 py-6 lg:px-0 lg:py-0">
      <div className="flex flex-col justify-between gap-4 lg:flex-row">
        <div className="heading-lg-medium-24 lg:text-white-500">
          API Management
        </div>
        <div className="flex gap-2 lg:gap-4">
          <AppButton
            variant="buy"
            size={isMobile ? "medium" : "large"}
            onClick={() => setIsShowModalChooseType(true)}
          >
            Create API
          </AppButton>
          <AppButton variant="secondary" size={isMobile ? "medium" : "large"}>
            Create TAX Report API
          </AppButton>
          <AppButton variant="secondary" size={isMobile ? "medium" : "large"}>
            Delete all API
          </AppButton>
        </div>
      </div>

      <div className="body-sm-regular-12 text-white-500 mt-6">
        <div>1. Each account can create up to 30</div>
        <div>
          2. API Keys Do not disclose your API Key, Secret Key (HMAC) or Private
          Key (Ed25519, RSA) to anyone to avoid asset losses. You should treat
          your API Key and your Secret Key (HMAC) or Private Key (Ed25519, RSA)
          like your passwords.
        </div>
        <div>
          3. It is recommended to restrict access to trusted IPs only to
          increase your account security
        </div>
        <div>
          4. You will not be to able to create an API Key if KYC is not
          completed
        </div>
      </div>

      <div className="bg-white-100 mb-8 mt-4 flex gap-2 rounded-[8px] px-2 py-4">
        <div
          className="cursor-pointer"
          onClick={() => setIsShowModalConfirmDefaultSecurityControls(true)}
        >
          <CheckboxCheckedIcon />
        </div>

        <div>
          <div className="body-md-regular-14">
            By checking this box, all existing API Key(s) on your master account
            and sub-accounts will be subject to Default Security Controls.
          </div>
          <div
            className="text-brand-500 body-md-regular-14 mt-2 flex cursor-pointer items-center gap-2"
            onClick={() =>
              setIsShowFullSecurityControls(!isShowFullSecurityControls)
            }
          >
            Default Security Controls Details{" "}
            <ChevronDownIcon
              className={isShowFullSecurityControls ? "rotate-[-180deg]" : ""}
            />
          </div>
        </div>
      </div>

      <APIItem />

      {isShowModalCreateAPI && (
        <ModalCreateAPI
          isOpen={isShowModalCreateAPI}
          onClose={() => setIsShowModalCreateAPI(false)}
        />
      )}
      
      {isShowModalConfirmDefaultSecurityControls && (
        <ModalConfirmDefaultSecurityControls
          isOpen={isShowModalConfirmDefaultSecurityControls}
          onClose={() => setIsShowModalConfirmDefaultSecurityControls(false)}
        />
      )}

      {isShowModalChooseType && (
        <ModalChooseAPIKeyType
          onCreateAPI={() => setIsShowModalCreateAPI(true)}
          isOpen={isShowModalChooseType}
          onClose={() => setIsShowModalChooseType(false)}
        />
      )}
    </div>
  );
};

export default ApiManagementPage;
